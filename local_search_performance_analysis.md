# 局部搜索性能瓶颈分析报告

## 🐌 问题现象

主进化过程之后的最后一次局部搜索特别慢，运行时间明显超过主进化过程中的局部搜索。

## 🔍 根本原因分析

### 1. **候选解数量爆炸性增长** ⭐⭐⭐⭐⭐

#### 问题描述
```python
# local_search.py 第72-79行
candidates = []
for x_ij in list(xi):                    # 遍历k个种子节点
    for neighbor in neighbors[x_ij]:     # 遍历每个种子的所有邻居
        if neighbor not in xi:           # 如果邻居不在当前种子集合中
            xi_new = xi.copy()           # 创建新的候选解
            xi_new.remove(x_ij)
            xi_new.add(neighbor)
            candidates.append((x_ij, neighbor, xi_new))
```

#### 数量计算
- **种子集合大小**: k = 50
- **平均节点度数**: 假设为d（通常在10-100之间）
- **候选解数量**: 最多 k × d = 50 × d 个

**实际案例**:
- blog网络: 平均度数约20，候选解数量 ≈ 50 × 20 = 1000个
- pgp网络: 平均度数约5，候选解数量 ≈ 50 × 5 = 250个

### 2. **迭代累积效应** ⭐⭐⭐⭐

#### 问题描述
```python
# local_search.py 第154-166行
for _ in range(max_iterations):  # 默认max_iterations=5
    improved_solution = local_search(...)  # 每次都要评估所有候选解
    if set(improved_solution) == set(current_solution):
        break
    current_solution = improved_solution
```

#### 计算复杂度
- **单次局部搜索**: O(k × d × PRE_cost)
- **迭代局部搜索**: O(max_iterations × k × d × PRE_cost)
- **总计算量**: 最多5 × 1000 = 5000次PRE计算

### 3. **PRE计算成本高** ⭐⭐⭐⭐

#### PRE算法复杂度
```python
# base_fun.py 第56-105行
def PRE(G, S, p, neighbors, max_hop=5):
    for _ in range(max_hop):           # 5轮迭代
        for v in nodes:                # 遍历所有节点
            for u in parents:          # 遍历所有邻居
                # 复杂度: O(max_hop × |V| × avg_degree)
```

#### 成本分析
- **节点数量**: |V| (blog: ~1222, pgp: ~10680)
- **单次PRE成本**: O(5 × |V| × avg_degree)
- **blog网络**: 5 × 1222 × 20 = 122,200 次基本操作
- **pgp网络**: 5 × 10680 × 5 = 267,000 次基本操作

### 4. **缓存失效问题** ⭐⭐⭐

#### 缓存键不一致
```python
# test.py 第467行 (NM函数内)
cache_key = frozenset(seed_set)

# local_search.py 第50行 (局部搜索内)
cache_key = tuple(sorted(seed_set))
```

#### 问题影响
- **缓存隔离**: NM主进化的缓存无法被局部搜索复用
- **重复计算**: 相同种子集合被重复计算PRE值
- **内存浪费**: 维护两套独立的缓存系统

### 5. **线程竞争和锁开销** ⭐⭐

#### 并发问题
```python
# local_search.py 第46-64行
cache_lock = threading.Lock()
def cached_PRE(seed_set):
    with cache_lock:  # 每次缓存访问都要加锁
        if cache_key in fitness_cache:
            return fitness_cache[cache_key]
    # ... PRE计算
    with cache_lock:  # 写入缓存也要加锁
        fitness_cache[cache_key] = fitness_value
```

#### 性能影响
- **锁竞争**: 12个线程竞争同一个锁
- **串行化**: 缓存访问变成串行操作
- **上下文切换**: 频繁的线程切换开销

## 📊 性能瓶颈量化分析

### 时间复杂度对比

| 阶段 | 候选解数量 | PRE计算次数 | 预估时间比例 |
|------|------------|-------------|--------------|
| 主进化单次局部搜索 | ~200 | ~200 | 1x |
| 最终迭代局部搜索 | ~1000 | ~5000 | 25x |

### 实际网络数据

#### Blog网络 (1222节点, 16714边)
- **平均度数**: ~27
- **候选解数量**: 50 × 27 = 1350个
- **最大PRE计算**: 5 × 1350 = 6750次
- **预估时间**: 主进化的30-50倍

#### PGP网络 (10680节点, 24316边)
- **平均度数**: ~4.6
- **候选解数量**: 50 × 4.6 = 230个
- **最大PRE计算**: 5 × 230 = 1150次
- **预估时间**: 主进化的5-10倍

## 🚀 优化建议

### 1. **限制候选解数量** (立即见效)
```python
# 限制每个种子节点的邻居候选数量
max_neighbors_per_seed = 5
for x_ij in list(xi):
    neighbors_list = list(neighbors[x_ij])
    # 按度数排序，选择前N个邻居
    top_neighbors = sorted(neighbors_list, 
                          key=lambda n: G.degree(n), 
                          reverse=True)[:max_neighbors_per_seed]
    for neighbor in top_neighbors:
        if neighbor not in xi:
            # 生成候选解
```

### 2. **统一缓存机制** (中等效果)
```python
# 在NM函数中传递缓存给局部搜索
optimized_seed = local_search_iterative(
    global_best_solution, G, p, k, neighbors, max_hop, 
    fitness_cache,  # 复用NM的缓存
    max_iterations=5, n_jobs=min(12, os.cpu_count() or 4)
)
```

### 3. **早停策略** (中等效果)
```python
# 如果连续N次迭代没有改进，提前停止
no_improvement_count = 0
for iteration in range(max_iterations):
    improved_solution = local_search(...)
    if set(improved_solution) == set(current_solution):
        no_improvement_count += 1
        if no_improvement_count >= 2:  # 连续2次无改进就停止
            break
    else:
        no_improvement_count = 0
```

### 4. **减少线程锁竞争** (小幅提升)
```python
# 使用线程本地缓存减少锁竞争
import threading
thread_local_cache = threading.local()

def cached_PRE_thread_safe(seed_set):
    if not hasattr(thread_local_cache, 'cache'):
        thread_local_cache.cache = {}
    
    cache_key = frozenset(seed_set)
    if cache_key in thread_local_cache.cache:
        return thread_local_cache.cache[cache_key]
    
    # 计算并缓存
    fitness_value = PRE(G, seed_set, p, neighbors, max_hop)
    thread_local_cache.cache[cache_key] = fitness_value
    return fitness_value
```

### 5. **自适应迭代次数** (智能优化)
```python
# 根据网络规模自适应调整迭代次数
def adaptive_max_iterations(G, k):
    avg_degree = sum(dict(G.degree()).values()) / G.number_of_nodes()
    candidate_count = k * avg_degree
    
    if candidate_count > 1000:
        return 2  # 大网络减少迭代
    elif candidate_count > 500:
        return 3
    else:
        return 5  # 小网络保持原有迭代次数
```

## 🎯 推荐的立即优化方案

1. **限制候选解数量**: 每个种子最多考虑5个最高度数的邻居
2. **统一缓存机制**: 让局部搜索复用NM的适应度缓存
3. **自适应迭代**: 根据网络规模调整max_iterations

预期效果: **减少70-80%的局部搜索时间**
